build:
  stage: build
  image: docker:latest 
  services:
    - docker:dind
  script:
    - export COMPOSE_PROFILES=production
    - export COMPOSE_FILE=docker/docker-compose.yml
    - export DOCKER_BUILDKIT=0  # More verbose output, but doesn't hang on errors
    - docker login -u "$ACR_USER" -p "$ACR_PASSWORD" boldidea.azurecr.io
    - docker-compose pull || true
    - docker-compose build --build-arg GIT_COMMIT_SHA=$CI_COMMIT_SHORT_SHA
    - |
      docker-compose config --images | while read image; do
        docker tag "$image:latest" "$image:$CI_COMMIT_SHORT_SHA"
        docker push "$image" --all-tags
      done
  only:
    refs:
      - main
      - production

deploy:
  # To create the service principal:
  #     az ad sp create-for-rbac --name GitLabCI --skip-assignment
  # To give permissions to a webapp:
  #     az role assignment create \
  #       --assignee $SP_APP_ID \
  #       --role  "Website Contributor" \
  #       --scope /subscriptions/{SubscriptionId}/resourceGroups/{ResourceGroupName}/providers/Microsoft.Web/sites/{WebAppName}
  stage: deploy
  image: mcr.microsoft.com/azure-cli
  script:
    - az login --service-principal -u $AZURE_APP_ID -p $AZURE_PASSWORD --tenant $AZURE_TENANT_ID
    - echo "Getting list of hive webapps..."
    - |
      az webapp list -g rg-boldidea-hive --query "[].name" --output tsv | while read webapp_name; do
        echo "Deploying changes to $webapp_name..."
        image="hive";
        [[ "$webapp_name" == "app-boldidea-hive-nginx" ]] && image="hive-nginx";
        az webapp config container set \
          --name "$webapp_name" \
          --resource-group "$AZURE_RESOURCE_GROUP" \
          --container-image-name "boldidea.azurecr.io/$image:$CI_COMMIT_SHORT_SHA" &&
        echo "Restarting $webapp_name"
        az webapp restart --name "$webapp_name" --resource-group "$AZURE_RESOURCE_GROUP"
      done;
  only:
    refs:
      - production
