from django.db import models
from django.db.models import (
    <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Q, When, F, ExpressionWrapper, Value)
from django.db.models.functions import ExtractYear
from django.utils import timezone

from hive.utils import Age, DatePart
from volunteers.constants import MENTOR_ROLES


class ClubQueryError(Exception):
    pass


class PastFutureQuerySetMixin:
    date_lookup = 'date'

    def past(self, as_of=None):
        as_of = as_of or timezone.now()
        lookup = self.date_lookup + '__lt'
        return self.filter(**{lookup: as_of})

    def future(self, as_of=None):
        as_of = as_of or timezone.now()
        lookup = self.date_lookup + '__gte'
        return self.filter(**{lookup: as_of})


def annotate_period_info(qs, club_lookup=None, prefix=''):
    lookup_prefix = ''

    if club_lookup is not None:
        lookup_prefix = f'{club_lookup}__'

    qs = qs.annotate(**{
        f'{prefix}year': ExtractYear(f'{lookup_prefix}start_date'),
        # FIXME Bold Idea now makes a distinction between "fiscal year" and "program year".
        # While our fiscal year starts July 1, we now define our "program year" as starting
        # with the first summer club. All references to "fiscal year" in hive should be changed
        # to "program year" match this new distinction (unless Jul 1 FY is actually needed).
        f'{prefix}fiscal_year': ExpressionWrapper(Case(
            When(
                Q(**{f'{lookup_prefix}period__in': ('SCHOOL_YEAR', 'SUMMER', 'FALL')}),
                then=(ExtractYear(f'{lookup_prefix}start_date') + 1)
            ),
            default=ExtractYear(f'{lookup_prefix}start_date')
        ), output_field=models.PositiveIntegerField()),
        f'{prefix}period_number': ExpressionWrapper(Case(
            When(Q(**{f'{lookup_prefix}period': 'SUMMER'}), then=Value(0)),
            When(Q(**{f'{lookup_prefix}period': 'SCHOOL_YEAR'}), then=Value(1)),
            When(Q(**{f'{lookup_prefix}period': 'FALL'}), then=Value(2)),
            When(Q(**{f'{lookup_prefix}period': 'SPRING'}), then=Value(3)),
        ), output_field=models.PositiveIntegerField()),
    })

    if lookup_prefix:
        # Annotate period if we're not using the Club model
        qs = qs.annotate(period=F(f'{lookup_prefix}period'))
    
    return qs

class PeriodQuerySetMixin:
    club_lookup = None

    def with_period_info(self, club_lookup=None):
        """
        Annotate queryset with "year" (which is different depending on period)
        and "period_number" (so we can order by period)
        """
        club_lookup = club_lookup or self.club_lookup
        qs = annotate_period_info(self, club_lookup=club_lookup)
        
        # Apply distinct() when we have a club_lookup to avoid duplicates from joins
        if club_lookup:
            qs = qs.distinct()
            
        return qs

    def for_year(self, year, fiscal=False):
        if fiscal:
            return self.with_period_info().filter(fiscal_year=year)
        else:
            return self.with_period_info().filter(year=year)

    def for_period(self, year, period=None, fiscal=False):
        if fiscal:
            qs = self.with_period_info().filter(fiscal_year=year)
        else:
            qs = self.with_period_info().filter(year=year)

        if period:
            return qs.filter(period=period)

        return qs


class PeriodQuerySet(PeriodQuerySetMixin, models.QuerySet):
    @classmethod
    def as_manager(cls, club_lookup=None):
        if club_lookup:
            new_cls = type('PeriodQuerySet', (cls,), {'club_lookup': club_lookup})
            return new_cls.as_manager()
        return super().as_manager()


class ClubQuerySet(PeriodQuerySetMixin, models.QuerySet):
    def _get_open_for_registration_q(self, as_of):
        return Q(
            Q(registration_close_date__isnull=True) |
            Q(registration_close_date__gte=as_of),
            registration_open_date__isnull=False,
            registration_open_date__lte=as_of,
            canceled=False
        )

    def get_by_natural_key(self, club_code, *args, **kwargs):
        return self.get(club_code=club_code)

    def with_end_date(self):
        return self.annotate(end_date=Max('sessions__date', filter=Q(sessions__canceled=False)))

    def open_for_registration(self, as_of=None):
        as_of = as_of or timezone.now()
        return self.filter(self._get_open_for_registration_q(as_of=as_of))

    def with_next_session_times(self, as_of=None):
        """
        Adds `next_session_start` and `next_session_end` to the queryset
        (includes currently running sessions)
        """
        from clubs.models import Session
        as_of = as_of or timezone.now()
        upcoming = Session.objects.filter(club=models.OuterRef('pk'), end_datetime__gte=as_of)
        return self.annotate(
            next_session_start=models.Subquery(
                upcoming.order_by('end_datetime').values('start_datetime')[:1]),
            next_session_end=models.Subquery(
                upcoming.order_by('end_datetime').values('end_datetime')[:1])
        )

    def current(self, only_started=False, as_of=None):
        """
        Includes all clubs current or future (unless only_started=True)
        """
        as_of = as_of or timezone.now()
        clubs = self.with_end_date()

        # don't include clubs that have ended
        current_q = Q(end_date__gte=as_of)

        if only_started:
            current_q &= Q(start_date__lte=as_of)

        q = current_q | self._get_open_for_registration_q(as_of=as_of)

        return clubs.filter(q).distinct()

    def with_volunteer_stats(self):
        """
        Adds `remaining_volunteers_needed` to the queryset.

        This is calculated by looking at individual session signups rather than all volunteers
        related to the club (some can be floaters, etc).
        """

        # TODO: upgrade to Django 2 and use filtered count feature instead of conditional
        # aggregation
        num_mentors = ExpressionWrapper(
            Sum(Case(
                When(volunteer_assignments__role__in=MENTOR_ROLES, then=1),
                default=0,
                output_field=models.IntegerField()
            )),
            output_field=models.IntegerField()
        )

        qs = self.annotate(
            num_mentors=num_mentors
        ).annotate(
            remaining_volunteers_needed=(F('num_volunteers_needed') - num_mentors)
        )
        return qs

    def with_attendance_stats(self):
        """
        Use when calculating attendance stats over multiple clubs to reduce number of queries
        needed
        """
        return self.prefetch_related(
            'sessions__volunteer_attendances', 'sessions__student_attendances')


class ClubManager(models.Manager.from_queryset(ClubQuerySet)):
    def get_queryset(self):
        # Include period info by default on all club objects
        return super().get_queryset().with_period_info()


class SemesterQuerySetMixin:
    semester_date_lookup = 'date'

    def with_semester(self):
        """
        Annotates sessions with semester (FALL, SPRING, SUMMER)
        """
        lookup = self.semester_date_lookup
        return self.annotate(
            semester=Case(
                When(Q(**{f'{lookup}__month__gte': 1, f'{lookup}__month__lte': 5}),
                     then=Value('SPRING')),
                When(Q(**{f'{lookup}__month__gte': 6, f'{lookup}__month__lte': 8}),
                     then=Value('SUMMER')),
                When(Q(**{f'{lookup}__month__gte': 9, f'{lookup}__month__lte': 12}),
                     then=Value('FALL')),
                output_field=CharField()
            )
        ).distinct()


class SessionQuerySet(
        SemesterQuerySetMixin, PastFutureQuerySetMixin, PeriodQuerySetMixin, models.QuerySet):
    semester_date_lookup = 'date'
    date_lookup = 'start_datetime'
    club_lookup = 'club'

    def with_attendance_stats(self):
        return self.annotate(
            num_volunteer_attendances=Count('volunteer_attendances'),
            num_student_attendances=Count('student_attendances')
        )

    def get_calendar(self, name=None):
        from clubs.utils import generate_session_calendar
        return generate_session_calendar(self, name=name)


class AttendanceQuerySet(SessionQuerySet):
    date_lookup = 'session__date'
    semester_date_lookup = date_lookup
    club_lookup = 'session__club'

    def get_calendar(self, name=None):
        from clubs.utils import generate_session_calendar
        return generate_session_calendar((a.session for a in self), name=name)


class VolunteerAttendanceQuerySet(AttendanceQuerySet):
    def verified(self):
        return self.filter(assignment__verified=True)


class StudentRegistrationQuerySet(PeriodQuerySetMixin, models.QuerySet):
    club_lookup = 'club'

    def with_student_age(self):
        """
        Annotates queryset with `student_age` representing student's age at the time of
        registration
        """
        as_of = models.F('date')
        return self.annotate(student_age=DatePart('year', Age(as_of, 'student__birth_date')))

    def by_status(self):
        return self.annotate(
            status_order=Case(
                When(status='COMPLETE', then=0),
                When(status='CANCELED', then=2),
                default=1,
                output_field=IntegerField()
            )
        ).order_by('status_order')


class AnnouncementQuerySet(PeriodQuerySetMixin, models.QuerySet):
    club_lookup = 'clubs'

    def current(self):
        now = timezone.now()
        return self.filter(
            Q(publish_date=None) | Q(publish_date__lte=now),
            Q(archive_date=None) | Q(archive_date__gt=now)
        )

    def for_volunteers(self):
        return self.filter(
            Q(visibility='ANYONE') | Q(visibility='VOLUNTEER')
        )

    def for_students(self):
        return self.filter(
            Q(visibility='ANYONE') | Q(visibility='STUDENT')
        )


class DiscountCodeQuerySet(models.QuerySet):
    def with_num_used(self):
        return self.annotate(
            num_used=Count('registrations', distinct=True)
        )
