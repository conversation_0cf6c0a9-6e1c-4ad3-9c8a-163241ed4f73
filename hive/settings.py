"""
Django settings for hive project.

Generated by 'django-admin startproject' using Django 1.10.2.

For more information on this file, see
https://docs.djangoproject.com/en/1.10/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/2.12/ref/settings/
"""

import json
import os
from datetime import timedelta

import sentry_sdk
from django.conf.locale.en import formats
from django.core.exceptions import ImproperlyConfigured
from django.utils.translation import gettext_lazy as _
from sentry_sdk.integrations.django import DjangoIntegration

# If APPCONFIG_ENDPOINT is set, pull in azure appconfig settings and enable
if os.environ.get('APPCONFIG_ENDPOINT'):
    # Load environment settings from appconfig
    from azure_support.config import configure_env
    configure_env()


# Deployment environment (dev, testing, production)
ENVIRONMENT = os.environ.get('HIVE_ENVIRONMENT')
VALID_ENVIRONMENTS = ('dev', 'testing', 'production')
if ENVIRONMENT not in VALID_ENVIRONMENTS:
    raise ImproperlyConfigured(
        f"'HIVE_ENVIRONMENT' must be set to one of {VALID_ENVIRONMENTS}")


SECRETS_PATH = os.environ.get('SECRETS_PATH', None)


# This is a workaround for Django VARCHAR fields being limited to 255 characters.
# Changing this setting will require a `makemigrations` across all apps.
MAX_VARCHAR_LENGTH = 1024 * 1024

def load_secret(name, default=None):
    """
    Loads the given secret from the approprate source.

    In production, secrets are loaded from a file (secrets in environment variables are considered
    somewhat insecure).

    In development environments, we can allow secrets to be stored in environment variables to make
    things easier.

    If SECRETS_PATH is defined, this will load the secret from a file in that directory with the
    same name as the secret.

    If the environment variable "{name}_FILE" exists, load the secret key from the given file path.

    Otherwise, if in a development environment, the secret will be loaded directly from the
    environment.
    """

    if SECRETS_PATH and os.path.exists(os.path.join(SECRETS_PATH, name)):
        with open(SECRETS_PATH, name) as f:
            return f.read()

    if os.environ.get(f'{name}_FILE'):
        with open(os.environ[f'{name}_FILE']) as f:
            return f.read()

    return os.environ.get(name, default)


# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.environ.get('DJANGO_DEBUG', os.environ.get('DEBUG')) == 'True'

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

SECRET_KEY = load_secret('DJANGO_SECRET_KEY')

# Note: you will not be able decrypt any data that is encrypted in production
# To make your own for development, create a random 32-byte string
AES_KEY = load_secret('AES_KEY')

# SSL certs dir for local development
LOCALDEV_SSL_DIR = os.environ.get('LOCALDEV_SSL_DIR', None)

# Are we running from a management script?
MANAGEMENT_SCRIPT = os.environ.get('MANAGEMENT_SCRIPT', False) and True

# Possible sites:
# - "hive" (default) - volunteer/staff portal
# - "student" - student portal
# - "clubs" - student/parent registration site
# - "api" - API urls
# - "sso" - Single-sign-on integrations
VALID_SITES = ('hive', 'student', 'clubs', 'api', 'sso')
SITE = os.environ.get('DJANGO_SITE', os.environ.get('HIVE_SITE', os.environ.get('SITE', 'hive')))
if SITE not in VALID_SITES:
    raise ValueError(f'Invalid DJANGO_SITE: "{SITE}". Use one of: {VALID_SITES}')

if not MANAGEMENT_SCRIPT:
    print(f'Using site: {SITE}')

# Domain configuration -- used in various places in hive to determine CORS settings & absolute URLS
SCHEME = os.environ.get('SCHEME', 'https')
BASE_DOMAIN = os.environ.get('BASE_DOMAIN', 'boldidea.local')
DOMAINS = {
    'hive': os.environ.get('HIVE_DOMAIN', f'hive.{BASE_DOMAIN}'),
    'clubs': os.environ.get('CLUBS_DOMAIN', f'clubs.{BASE_DOMAIN}'),
    'student': os.environ.get('STUDENT_DOMAIN', f'my.{BASE_DOMAIN}'),
    'sso': os.environ.get('SSO_DOMAIN', f'sso.{BASE_DOMAIN}'),
    'api': os.environ.get('API_DOMAIN', f'api.{BASE_DOMAIN}'),
}

DOMAIN = DOMAINS[SITE]

USE_DOCKER = os.environ.get('USE_DOCKER') == 'True'

if ENVIRONMENT == 'dev':
    if USE_DOCKER:
        DEV_PORTS = {site: 8000 for site in VALID_SITES}
    else:
        DEV_PORTS = {
            'hive': 8000,
            'clubs': 8001,
            'student': 8002,
            'sso': 8003,
            'api': 8004,
        }
    DEV_PORT = DEV_PORTS[SITE]

ALLOWED_HOSTS = [
    '127.0.0.1',
]

# Use sesssion-based CSRF tokens instead of cookie to make cross-site requests easier
CSRF_USE_SESSIONS = True
CSRF_TRUSTED_ORIGINS = []

for site in VALID_SITES:
    ALLOWED_HOSTS.append(DOMAINS[site])
    CSRF_TRUSTED_ORIGINS.append(DOMAINS[site])
    if ENVIRONMENT == 'dev':
        if USE_DOCKER:
            ALLOWED_HOSTS.append(f'{DOMAINS[site]}:{DEV_PORT}')
            CSRF_TRUSTED_ORIGINS.append(f'{DOMAINS[site]}:{DEV_PORT}')
        else:
            ALLOWED_HOSTS.append(f'{DOMAINS[site]}:{DEV_PORT}')
            CSRF_TRUSTED_ORIGINS.append(f'{DOMAINS[site]}:{DEV_PORT}')

if SITE == 'api':
    CORS_ALLOWED_ORIGIN_REGEXES = [
        SCHEME + r'://\w+\.' + BASE_DOMAIN.replace('.', '\\.') + r'(:\d+)?$',
        r'https://.+\.codio\.io$',
    ]
    from corsheaders.defaults import default_headers
    CORS_ALLOW_HEADERS = default_headers + (
        'x-http-method-override',
        'x-csrf-token',
    )
    CORS_ALLOW_CREDENTIALS = True

# Allow gateways to proxy requests
USE_X_FORWARDED_HOST = True

INTERNAL_IPS = [
    '127.0.0.1',
    '**********',
]

if os.environ.get('ALLOWED_HOSTS'):
    ALLOWED_HOSTS.extend(os.environ['ALLOWED_HOSTS'].split(','))


# For production, this is ".boldidea.org", for local dev, use ".boldidea.local"
SESSION_COOKIE_DOMAIN = os.environ.get('SESSION_COOKIE_DOMAIN', f'.{BASE_DOMAIN}')
SESSION_COOKIE_SECURE = SCHEME == 'https'
CSRF_COOKIE_DOMAIN = os.environ.get('CSRF_COOKIE_DOMAIN', SESSION_COOKIE_DOMAIN)


# Authentication
AUTH_USER_MODEL = 'users.User'
USERNAME_FIELD = 'email'

if SITE == 'student':
    USERNAME_FIELD = 'username'
    AUTH_USER_MODEL = 'students.StudentUser'
elif SITE == 'sso':
    USERNAME_FIELD = 'username'

AUTHENTICATION_BACKENDS = (
    'hive.auth.MultiSiteAuthBackend',
)

STUDENT_USER_EMAIL_DOMAIN = os.environ.get('STUDENT_USER_EMAIL_DOMAIN', 'my.boldidea.org')

# Course handler
if SITE == 'student':
    COURSE_HANDLER = 'student_portal.course_handler.CourseHandler'
else:
    COURSE_HANDLER = 'dashboard.course_handler.CourseHandler'


# Apps
INSTALLED_APPS = [
    'api',
    'users',
    'accounts',
    'contacts',
    'students',
    'volunteers',
    'clubs',
    'programs',  # FIXME: remove this - moved to "clubs"
    'surveys',
    'billing',
    'courses',
    'sso',
    'ide',  # FIXME: remove this when we no longer need "old IDE downloads"
    'studio',
    'questionnaires',
    'chat',
    'hive',
    'channels',
    'taggit',
    'taggit_serializer',
    'slackbot',
    'slack_utils',
    'material.theme.purple',
    'material',
    'markupfield',
    'martor',
    'django_json_widget',
    'compressor',
    'easy_thumbnails',
    'import_export',
    'hive.apps.HiveAdminConfig',
    'vinaigrette',
    'corsheaders',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.sites',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.postgres',
    'django.contrib.humanize',
]

if SITE == 'hive' or MANAGEMENT_SCRIPT:
    INSTALLED_APPS = ['dashboard'] + INSTALLED_APPS

if SITE == 'student' or MANAGEMENT_SCRIPT:
    INSTALLED_APPS = ['student_portal'] + INSTALLED_APPS

CHAT_ENABLED = os.environ.get('CHAT_ENABLED') == 'True'
if not CHAT_ENABLED:
    INSTALLED_APPS.remove('channels')

if os.environ.get('AZURE_SUPPORT_ENABLED'):
    INSTALLED_APPS = ['azure_support'] + INSTALLED_APPS

# Channels (websocket chat) settings
REDIS_HOST = os.environ.get('REDIS_HOST', '127.0.0.1')
REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
REDIS_DB = int(os.environ.get('REDIS_DB', 0))
ASGI_APPLICATION = "hive.routing.application"
CHANNEL_LAYERS = {
    'default': {
        'BACKEND': 'channels_redis.core.RedisChannelLayer',
        'CONFIG': {
            'hosts': [(REDIS_HOST, REDIS_PORT)],
        },
    },
}

CHAT_DEFAULT_CHANNEL = 'general'
CHAT_STATUS_TYPES = ('help', 'dnd')
CHAT_CHANNEL_TYPES = (
    ('CHANNEL', 'Channel'),
    ('STUDENT', 'Student'),
    ('DIRECT_MESSAGE', 'Direct message'),
)

MATERIAL_ADMIN_SITE = 'hive.admin.admin_site'

SITE_ID = 1

ADMIN_APPS = [
    'users', 'volunteers', 'accounts', 'clubs', 'courses', 'students', 'questionnaires',
    'api', 'sso', 'surveys', 'billing', 'studio',
]

MIDDLEWARE = []


# used for testing
SHIFT_TIME = None
if os.environ.get('SHIFT_TIME'):
    SHIFT_TIME = int(os.environ['SHIFT_TIME'])
    MIDDLEWARE.append('hive.middleware.FreezeTimeMiddleware')


MIDDLEWARE.extend([
    'django.middleware.security.SecurityMiddleware',
    'hive.middleware.SameSiteNoneMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'hive.middleware.ValidateDomainMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'hive.middleware.CodioIframeMiddleware',
    'hive.middleware.MartorPreviewFix',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'users.middleware.UserLanguageMiddleware',
    'hive.middleware.AdminLanguageMiddleware',
    'api.middleware.MethodOverrideMiddleware',
])


# Allow codio to run in iframe
X_FRAME_OPTIONS = 'ALLOW-FROM https://codio.com/'
CSRF_TRUSTED_ORIGINS.extend([
    'codio.com',
    '*.codio.com',
])


if SITE == 'clubs':
    MIDDLEWARE.append('clubs.middleware.DiscountCodeMiddleware')

if DEBUG:
    INSTALLED_APPS.append('django_extensions')
    if os.environ.get('DEBUG_TOOLBAR'):
        INSTALLED_APPS.append('debug_toolbar')
        MIDDLEWARE = ['debug_toolbar.middleware.DebugToolbarMiddleware'] + MIDDLEWARE

ROOT_URLCONF = f'hive.site_urlconfs.{SITE}'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'hive.context_processors.site',
                'hive.context_processors.django_settings',
                'api.context_processors.api_vars',
                # 'social_django.context_processors.backends',
                # 'social_django.context_processors.login_redirect',
            ],
        },
    },
]

if SITE == 'clubs':
    TEMPLATES[0]['OPTIONS']['context_processors'].extend([
        'clubs.context_processors.session_cart',
    ])

if SITE == 'student':
    TEMPLATES[0]['OPTIONS']['context_processors'].extend([
        'student_portal.context_processors.club_registration_info',
    ])

if os.environ.get('ERROR_TEMPLATE_DIR'):
    TEMPLATES[0]['DIRS'].append(os.environ['ERROR_TEMPLATE_DIR'])


WSGI_APPLICATION = 'hive.wsgi.application'


# Database
# https://docs.djangoproject.com/en/2.12/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'hive.db_backends.postgis',
        'NAME': os.environ.get('DJANGO_DB_NAME', 'hive'),
        'HOST': os.environ.get('DJANGO_DB_HOST', 'localhost'),
        'USER': os.environ.get('DJANGO_DB_USER', None),
        'PASSWORD': os.environ.get('DJANGO_DB_PASSWORD', None),
    },
}

if os.environ.get('DJANGO_DB_REQUIRE_SSL') == 'True':
    DATABASES['default']['OPTIONS'] = {'sslmode': 'require'}

# Internationalization
# https://docs.djangoproject.com/en/2.12/topics/i18n/

LANGUAGE_CODE = os.environ.get('DJANGO_LANGUAGE_CODE', 'en')

LANGUAGES = [
    ('en', _('English')),
    ('es', _('Spanish')),
]

LOCALE_PATHS = (
    os.path.join(BASE_DIR, "locale"),
)


TIME_ZONE = 'America/Chicago'

USE_I18N = True

USE_L10N = True

USE_TZ = True

DATE_INPUT_FORMATS = ('%m/%d/%Y', '%m/%d/%y', '%Y-%m-%d',
                      '%b %d %Y', '%b %d, %Y', '%d %b %Y',
                      '%d %b, %Y', '%B %d %Y', '%B %d, %Y',
                      '%d %B %Y', '%d %B, %Y')
formats.DATE_INPUT_FORMATS = DATE_INPUT_FORMATS

TIME_INPUT_FORMATS = ('%I:%M %p', '%I:%M%p', '%H:%M',
                      '%I:%M:%S %p', '%I:%M:%S%p', '%H:%M:%S')


CSV_DATE_FORMAT = 'Y-m-d H:i:s O'

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/2.12/howto/static-files/

STATIC_URL = '/static/'
MEDIA_URL = '/media/'

STATIC_ROOT = os.environ.get('DJANGO_STATIC_ROOT', None)
MEDIA_ROOT = os.environ.get('DJANGO_MEDIA_ROOT', None)

STATICFILES_FINDERS = (
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    'compressor.finders.CompressorFinder',
)

FILE_UPLOAD_DIRECTORY_PERMISSIONS = 0o775
FILE_UPLOAD_PERMISSIONS = 0o664

COMPRESS_ENABLED = True
COMPRESS_PRECOMPILERS = (
    ('text/x-scss', 'django_libsass.SassCompiler'),
)
COMPRESS_FILTERS = {
    'css': [
        'compressor.filters.css_default.CssAbsoluteFilter',
    ],
    'js': ['compressor.filters.jsmin.JSMinFilter'],
}

if DEBUG:
    COMPRESS_FILTERS['js'] = []

COMPRESS_OFFLINE = os.environ.get('COMPRESS_OFFLINE') is not None
if COMPRESS_OFFLINE:
    COMPRESS_OFFLINE_TIMEOUT = 9_999_999_999  # Always use offline "cache"
COMPRESS_ROOT = os.environ.get('COMPRESS_ROOT', STATIC_ROOT)
COMPRESS_URL = os.environ.get('COMPRESS_URL', STATIC_URL)

FISCAL_START_MONTH = os.environ.get('FISCAL_START_MONTH', 7)

# Braintree
BRAINTREE_TOKENIZATION_KEY = os.environ.get('BRAINTREE_TOKENIZATION_KEY')
BRAINTREE_MERCHANT_ID = os.environ.get('BRAINTREE_MERCHANT_ID')
if os.environ.get('BRAINTREE_ENVIRONMENT'):
    import braintree
    braintree.Configuration.configure(
        os.environ.get('BRAINTREE_ENVIRONMENT', 'sandbox'),
        merchant_id=BRAINTREE_MERCHANT_ID,
        public_key=os.environ.get('BRAINTREE_PUBLIC_KEY'),
        private_key=load_secret('BRAINTREE_PRIVATE_KEY'),
    )

# Stripe
STRIPE_API_KEY = os.environ.get('STRIPE_API_KEY')

# 1st Pay Gateway
FPG_MERCHANT_KEY = os.environ.get('FPG_MERCHANT_KEY')
FPG_PROCSESOR_ID = os.environ.get('FPG_PROCSESOR_ID')
FPG_TIME_ZONE = os.environ.get('FPG_TIME_ZONE')

# Xero keys
XERO_CONSUMER_KEY = os.environ.get('XERO_CONSUMER_KEY', '')
XERO_RSA_KEY = ''
if os.environ.get('XERO_RSA_KEY_FILE'):
    with open(os.environ['XERO_RSA_KEY_FILE']) as _xero_rsa_key:
        XERO_RSA_KEY = _xero_rsa_key.read()

# QuickBooks
QUICKBOOKS_ENVIRONMENT = os.environ.get('QUICKBOOKS_ENVIRONMENT')  # 'sandbox' or 'production'
QUICKBOOKS_CLIENT_ID = os.environ.get('QUICKBOOKS_CLIENT_ID')
QUICKBOOKS_CLIENT_SECRET = load_secret('QUICKBOOKS_CLIENT_SECRET')
QUICKBOOKS_COMPANY_ID = os.environ.get('QUICKBOOKS_COMPANY_ID')
QUICKBOOKS_REDIRECT_URI = os.environ.get(
    'QUICKBOOKS_REDIRECT_URI',
    f'https://hive.{BASE_DOMAIN}/admin/qbauth/',
)

# Accounting settings
PROGRAM_FEE_ITEM_ID = os.environ.get('PROGRAM_FEE_ITEM_ID')
PROGRAM_FEE_DISCOUNT_ITEM_ID = os.environ.get('PROGRAM_FEE_DISCOUNT_ITEM_ID')
INVOICE_URL = os.environ.get('INVOICE_URL', 'https://clubs.boldidea.org/invoice/{id}/')
MERCHANT_DEPOSIT_ACCOUNTS = json.loads(os.environ.get('MERCHANT_DEPOSIT_ACCOUNTS', '{}'))
CUSTOMER_TYPES = json.loads(os.environ.get('CUSTOMER_TYPES', '{}'))
SALES_ITEMS = json.loads(os.environ.get('SALES_ITEMS', '{}'))

VOLUNTEER_PROFILE_RENEWAL_PERIOD = timedelta(days=365.25)
VOLUNTEER_TERMS_RENEWAL_PERIOD = VOLUNTEER_PROFILE_RENEWAL_PERIOD
VOLUNTEER_SIGNUP_REQUIRE_LEGAL_NAME_AND_DOB = False

LOGIN_URL = '/login/'
LOGIN_REDIRECT_URL = '/'

# Email settings
DEFAULT_FROM_EMAIL = '<EMAIL>'
EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
if os.environ.get('DJANGO_EMAIL_HOST'):
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    EMAIL_HOST = os.environ.get('DJANGO_EMAIL_HOST')
    EMAIL_HOST_USER = os.environ.get('DJANGO_EMAIL_HOST_USER')
    EMAIL_HOST_PASSWORD = os.environ.get('DJANGO_EMAIL_HOST_PASSWORD')
    EMAIL_USE_TLS = os.environ.get('DJANGO_EMAIL_USE_TLS') == 'True'
elif os.environ.get('AZURE_COMMUNICATION_CONNECTION_STRING'):
    EMAIL_BACKEND = 'django_azure_communication_email.EmailBackend'
    AZURE_COMMUNICATION_CONNECTION_STRING = os.environ.get('AZURE_COMMUNICATION_CONNECTION_STRING')

if os.environ.get('REGISTRATION_NOTIFICATION_RECIPIENTS'):
    REGISTRATION_NOTIFICATION_RECIPIENTS = (
        os.environ['REGISTRATION_NOTIFICATION_RECIPIENTS'].split(',')
    )
else:
    REGISTRATION_NOTIFICATION_RECIPIENTS = [DEFAULT_FROM_EMAIL]

MAILCHIMP_API_KEY = os.environ.get('MAILCHIMP_API_KEY')
MAILCHIMP_LIST_ID = os.environ.get('MAILCHIMP_LIST_ID')

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    # https://docs.python.org/2/library/logging.html#logrecord-attributes
    'formatters': {
        'basic': {
            'format': '%(asctime)s %(levelname)s %(name)s: %(message)s',
            'datefmt': '%b %d %H:%M:%S',
        },
        'simple': {
            'format': '%(levelname)s %(name)s: %(message)s',
        },
    },
    'filters': {
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    'handlers': {
        'logfile': {
            'level': 'INFO',
            'formatter': 'basic',
            'class': 'logging.FileHandler',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        '': {
            'level': os.environ.get('DJANGO_LOG_LEVEL', 'INFO'),
            'handlers': [],
        },
        'azure.core': {
            'level': 'WARNING',
        },
    },
}

DEBUG_APPS = ['billing', 'billing.qb']

if os.environ.get('DEBUG_SQL'):
    DEBUG_APPS.append('django.db.backends')
    LOGGING['handlers']['console']['level'] = 'DEBUG'
    handler_filters = LOGGING['handlers']['console'].get('filters', [])
    handler_filters.append('require_debug_true')

if not os.environ.get('DISABLE_CONSOLE_LOGGING'):
    LOGGING['loggers']['']['handlers'].append('console')


if DEBUG:
    # Set DEBUG log level for apps in DEBUG_APPS
    for app in DEBUG_APPS:
        if not LOGGING['loggers'].get(app):
            LOGGING['loggers'][app] = {}
        LOGGING['loggers'][app]['level'] = 'DEBUG'

if os.environ.get('DJANGO_LOG_FILE'):
    LOGGING['handlers']['logfile']['filename'] = os.environ['DJANGO_LOG_FILE']
    LOGGING['loggers']['']['handlers'].append('logfile')
else:
    del LOGGING['handlers']['logfile']


# --| Database backup job |---------------------------------
DB_BACKUP = None
if os.environ.get('DB_BACKUP_DIR'):
    DB_BACKUP = {
        'base_dir': os.environ['DB_BACKUP_DIR'],
        'dump_args': '-Fc',
        'keep_daily': 7,
        'keep_weekly': 5,
        'keep_monthly': 12,
        'weekly_rotate_weekday': '0',
        'monthly_rotate_day': '01',
        'yearly_rotate_date': '01/01',
    }


# --| Scheduled jobs |--------------------------------------
JOBS = [
    {'manage': 'db_backup', 'cron': {'hour': 1}},
    {'manage': 'process_registrations', 'cron': {'hour': 7}},
    {'manage': 'process_payments', 'cron': {'hour': 8}},
    {'manage': 'sync_badges', 'cron': {'hour': 9}},
    {'manage': 'refresh_qbauth', 'cron': {'minute': '*/15'}},
]

# --| Google |--------------------------
GOOGLE_APPLICATION_CREDENTIALS_JSON_B64 = os.environ.get(
    'GOOGLE_APPLICATION_CREDENTIALS_JSON_B64', None,
)
GOOGLE_APPLICATION_CREDENTIALS_FILE = os.environ.get('GOOGLE_APPLICATION_CREDENTIALS_FILE', None)


GOOGLE_ADMIN_ACCOUNT = '<EMAIL>'
GOOGLE_ADMIN_CUSTOMER_ID = os.environ.get('GOOGLE_ADMIN_CUSTOMER_ID', '')

GOOGLE_STUDENT_ORG_PATH = '/Students'
GOOGLE_STUDENT_ACCOUNT_DOMAIN = STUDENT_USER_EMAIL_DOMAIN

GOOGLE_MAPS_API_KEY = os.environ.get('GOOGLE_MAPS_API_KEY', '')
GEOCODE_API_KEY = os.environ.get('GEOCODE_API_KEY', '')


# --| LDAP |--------------------------
LDAP_SERVER = os.environ.get('LDAP_SERVER')
LDAP_ADMIN_DN = os.environ.get('LDAP_ADMIN_DN')
LDAP_ADMIN_PASS = load_secret('LDAP_ADMIN_PASS')
LDAP_USER_BASE_DN = os.environ.get('LDAP_USER_BASE_DN')
LDAP_GROUP_BASE_DN = os.environ.get('LDAP_GROUP_BASE_DN')


# --| Badgr |--------------------------
BADGR_USERNAME = load_secret('BADGR_USERNAME', '')
BADGR_PASSWORD = load_secret('BADGR_PASSWORD', '')
BADGR_ISSUER_ID = os.environ.get('BADGR_ISSUER_ID', '')
BADGR_RECIPIENT_FORMAT = os.environ.get(
    'BADGR_RECIPIENT_FORMAT', f'{{student.student_id}}@{DOMAINS["student"]}')

BADGES_ENABLED = all((BADGR_USERNAME, BADGR_PASSWORD, BADGR_ISSUER_ID, BADGR_RECIPIENT_FORMAT))

# --| Sentry |------------------------
SENTRY_DSN = os.environ.get('SENTRY_DSN', None)
SENTRY_ENVIRONMENT = os.environ.get('SENTRY_ENVIRONMENT', ENVIRONMENT)
SENTRY_FRONTEND_DSN = os.environ.get('SENTRY_FRONTEND_DSN', None)

if not DEBUG and SENTRY_DSN is not None:
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        environment=SENTRY_ENVIRONMENT,
        integrations=[DjangoIntegration()],

        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production,

        # traces_sample_rate=1.0,

        # If you wish to associate users to errors (assuming you are using
        # django.contrib.auth) you may enable sending PII data.
        send_default_pii=True,

        # By default the SDK will try to use the SENTRY_RELEASE
        # environment variable, or infer a git commit
        # SHA as release, however you may want to set
        # something more human-readable.
        # release="myapp@1.0.0",
    )


# Pre-loaded imports for ./manage.py shell
SHELL_PLUS_PRE_IMPORTS = [
    ('hive.utils', '*'),
]

SHELL_PLUS_DONT_LOAD = ['programs', 'sessions']

# models for `manage.py graph_models`
GRAPH_MODELS = {
  'all_applications': False,
  'group_models': True,
}

# Django Rest Framework settings
REST_FRAMEWORK = {
    'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',
    ),
    'DEFAULT_PARSER_CLASSES': (
        'rest_framework.parsers.JSONParser',
    ),
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'sso.api.auth.SSOTokenAuthentication',
        'api.auth.ServiceAccountAuthentication',
        'rest_framework.authentication.SessionAuthentication',
    ),
}

# Guacamole API URL (FIXME: remove this, guacamole workspaces no longer used)
GUACAMOLE_BASE_URL = os.environ.get('GUACAMOLE_BASE_URL', 'https://workspaces.boldidea.org')
TEST_AUTH_TOKEN = os.environ.get('TEST_AUTH_TOKEN')

# Slack tokens
SLACK_ACCESS_TOKEN = load_secret('SLACK_ACCESS_TOKEN', None)
SLACK_SIGNING_SECRET = load_secret('SLACK_SIGNING_SECRET', None)

# IDE settings (no longer used)
IDE_CONTROLLER_URL = os.environ.get('IDE_CONTROLLER_URL', 'NOT_SET')
IDE_STARTING_UID = int(os.environ.get('IDE_STARTING_UID', 6000))
IDE_STARTING_GID = int(os.environ.get('IDE_STARTING_GID', IDE_STARTING_UID))
IDE_PROJECT_EXPORT_DIR = MEDIA_ROOT and os.path.join(MEDIA_ROOT, 'ide-project-exports') or None
IDE_PROJECT_EXPORT_URL = f'{MEDIA_URL}/ide-project-exports/'

# Codio settings
CODIO_LTI_URL = os.environ.get('CODIO_LTI_URL')
CODIO_LTI_CONSUMER = load_secret('CODIO_LTI_CONSUMER')
CODIO_LTI_SECRET = load_secret('CODIO_LTI_SECRET')

LTI_AUTO_LAUNCH = os.environ.get('LTI_AUTO_LAUNCH') in ('True', 'T', 'TRUE', '1')

# Gitlab
GITLAB_SERVER = os.environ.get('GITLAB_SERVER', 'https://gitlab.com')

# This access token is currently defined under the `<EMAIL>` user on gitlab.
# The <EMAIL> account is authenticated on gitlab via Google login
GITLAB_ACCESS_TOKEN = load_secret('GITLAB_ACCESS_TOKEN')
STUDIO_PROJECT_URL_TEMPLATE = 'https://{project.slug}.studio.boldidea.org'

# Azure
AZURE_STORAGE_ACCT_KEY = load_secret('AZURE_STORAGE_ACCT_KEY')

# Martor (markdown editor)
MARTOR_THEME = 'semantic'
