from django.contrib.gis.db.backends.postgis.base import (
    DatabaseWrapper as PostGISDatabaseWrapper,
)


class DatabaseWrapper(PostGISDatabaseWrapper):
    """
    This is needed because the default postgis backend tries to create the postgis extension
    on every connection when it does not have permission to do so. The postgis extension must be
    created manually on the server.
    """

    def prepare_database(self):
        # Override to skip `CREATE EXTENSION` command for postgis
        with connection.cursor() as cursor:
            cursor.execute(
                "SELECT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'postgis');"
            )
            postgis_installed = cursor.fetchone()[0]

        # Skip creating the extension if it's already installed
        if not postgis_installed:
            with self.connection.cursor() as cursor:
                cursor.execute("CREATE EXTENSION postgis")
