ARG IMAGE=python:3.9-slim
ARG POETRY_VERSION=1.4.2
ARG POETRY_HOME=/opt/poetry
ARG POETRY_CACHE_DIR=/opt/poetry/cache
ARG POETRY_CONFIG_DIR=/opt/poetry/config


FROM ${IMAGE} as image-base

# -----------------------------------------------------------------------------
# build-base: system dependencies & python build system
# -----------------------------------------------------------------------------
FROM image-base as build-base

RUN apt-get -y update
RUN apt-get install -y --no-install-recommends build-essential gcc gdal-bin \
lcov libgdal-dev libpq-dev libsasl2-dev libssl-dev tox valgrind

ARG POETRY_VERSION
ARG POETRY_HOME
ARG POETRY_CACHE_DIR
ARG POETRY_CONFIG_DIR

ENV PYTHONFAULTHANDLER=1 \
    PYTHONHASHSEED=random \
    PYTHONUNBUFFERED=1 \
    PIP_DEFAULT_TIMEOUT=100 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_NO_CACHE_DIR=1 \
    POETRY_HOME=${POETRY_HOME} \
    POETRY_CACHE_DIR=${POETRY_CACHE_DIR} \
    POETRY_CONFIG_DIR=${POETRY_CONFIG_DIR}

WORKDIR /build

# Set up poetry
RUN python -m venv $POETRY_HOME && \
    $POETRY_HOME/bin/pip install -U pip && \
    $POETRY_HOME/bin/pip install poetry==${POETRY_VERSION}

# Set up the main venv
RUN python -m venv /venv
ENV VIRTUAL_ENV=/venv
ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# Update pip
RUN /venv/bin/pip install -U pip

# ------------------------------------------------------------------------------
# build: python dependencies & hive build
# ------------------------------------------------------------------------------
FROM build-base as build

# Install python dependencies
COPY pyproject.toml poetry.lock ./

RUN /venv/bin/pip install wheel

RUN ${POETRY_HOME}/bin/poetry install --no-interaction --only=main --no-root

## alternative to `poetry install`:
#RUN ${POETRY_HOME}/bin/poetry export -f requirements.txt -o requirements.txt \
#    --only=main --no-interaction
#RUN /venv/bin/pip install -r requirements.txt

# Build wheel for production use
COPY . .

# In order to build hive with the top-level packages, we need a hack to put them
# into pyproject.toml:
RUN ${POETRY_HOME}/bin/pip install toml &&\
    ${POETRY_HOME}/bin/python scripts/update-pyproject-packages.py

RUN ${POETRY_HOME}/bin/poetry build

# ------------------------------------------------------------------------------
# static-build: Static files build stage
# ------------------------------------------------------------------------------
# This is used as a base to copy over static files into the nginx stage
FROM build as static-build
ARG HIVE_ENVIRONMENT

ENV DJANGO_STATIC_ROOT=/hive/static
ENV COMPRESS_ROOT=/hive/static
ENV COMPRESS_OFFLINE=True

# Install hive so we can run manage command
COPY --from=build /build/dist/*.whl dist/
RUN /venv/bin/pip install dist/*.whl

# Build & collect staticfiles for later
RUN DJANGO_SECRET_KEY=collectstatic-tmp MANAGEMENT_SCRIPT=1 /venv/bin/manage compress
RUN DJANGO_SECRET_KEY=collectstatic-tmp MANAGEMENT_SCRIPT=1 /venv/bin/manage collectstatic --noinput

# ------------------------------------------------------------------------------
# base: Base stage (system deps and app paths)
# ------------------------------------------------------------------------------
FROM image-base as base

# Note: postgresql-client is needed for the pg_dump binary for db backup job
RUN apt-get -y update && \
    apt-get install -y --no-install-recommends gdal-bin postgresql-client

RUN useradd -Md /hive hive
WORKDIR /hive

RUN mkdir /hive/media && \
    mkdir /hive/db-backups && \
    chown -R hive:hive /hive

# ------------------------------------------------------------------------------
# base-app: Base app stage
# ------------------------------------------------------------------------------
FROM base as base-app

# copy venv from build stage
COPY --from=build --chown=hive:hive /venv /venv

# ------------------------------------------------------------------------------
# nginx: Nginx gateway service
# ------------------------------------------------------------------------------
# With Azure App Services, we can't share volumes between containers, so we have
# to build  nginx alongside the base image and use that to serve as the primary
# gateway as well as to serve static files. This allows us to keep our config in
# version control, and use the existing python base to collect static files.
FROM nginx:alpine as nginx-prod
ARG DJANGO_STATIC_ROOT=/hive/static
ARG GIT_COMMIT_SHA
LABEL org.label-schema.vcs-ref=$GIT_COMMIT_SHA

COPY --from=static-build /hive/static /hive/static

COPY ./docker/configs/prod/nginx /etc/nginx/templates

# ------------------------------------------------------------------------------
# prod: Production build
# ------------------------------------------------------------------------------
FROM base-app as prod

ARG WSGI_APP=hive.wsgi:application
ARG WSGI_PORT=8000
ARG GIT_COMMIT_SHA
LABEL org.label-schema.vcs-ref=$GIT_COMMIT_SHA

ENV PYTHONFAULTHANDLER=1 \
    PYTHONHASHSEED=random \
    PYTHONUNBUFFERED=1 \
    WSGI_APP=${WSGI_APP} \
    WSGI_PORT=${WSGI_PORT} \
    WORKERS=3 \
    THREADS=1 \
    VIRTUAL_ENV=/venv \
    PATH="/venv/bin:$PATH"

# Install built hive package
COPY --from=build /build/dist/*.whl dist/

# Copy django-compress cache manifest
# Note: if django-compress can't find this, it will give an error like this:
#
#   "You have offline compression enabled but key XXXXX is missing..."
#
# We don't need to copy the whole static dir b/c they will be copied to the
# nginx container.
COPY --from=static-build \
    /hive/static/CACHE/manifest.json \
    /hive/static/CACHE/manifest.json

RUN /venv/bin/pip install dist/*.whl

# Add openssh-server server for azure webapp SSH access
# See: https://learn.microsoft.com/en-us/azure/app-service/configure-custom-container
RUN apt-get install -y --no-install-recommends openssh-server && \
    useradd -d /hive -G hive -s /usr/bin/bash hive-admin && \
    echo "hive-admin:BoldIdeaHive!" | chpasswd
COPY ./docker/configs/prod/sshd_config /etc/ssh/sshd_config

COPY ./docker/configs/prod/docker-entrypoint.sh /docker-entrypoint.sh

# Clean up apt
RUN apt-get remove --purge --auto-remove -y && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists

CMD bash --noprofile --norc /docker-entrypoint.sh

# ------------------------------------------------------------------------------
# dev: Local dev build
# ------------------------------------------------------------------------------
FROM base-app as dev
ARG POETRY_HOME
ARG POETRY_CACHE_DIR
ARG POETRY_CONFIG_DIR

ENV PYTHONFAULTHANDLER=1 \
    PYTHONHASHSEED=random \
    PYTHONUNBUFFERED=1 \
    VIRTUAL_ENV=/venv \
    POETRY_CACHE_DIR=${POETRY_CACHE_DIR} \
    POETRY_CONFIG_DIR=${POETRY_CONFIG_DIR}

ENV PATH="$VIRTUAL_ENV/bin:$PATH"

# Install system packages useful for building newly added python deps
RUN apt-get install -y --no-install-recommends \
    build-essential gcc lcov valgrind curl libffi-dev libssl-dev

#RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash
RUN /venv/bin/pip install azure-cli

WORKDIR /src

# Bring in poetry from build stage
COPY --from=build --chown=hive:hive ${POETRY_HOME} ${POETRY_HOME}

# Copy hive src (though this should be mounted via docker volume)
COPY . .

# Update image w/ dev packages
RUN ${POETRY_HOME}/bin/poetry install --with=dev

# Make /hive/static writable for development
RUN mkdir /hive/static && chown -R hive:hive /hive/static

USER hive

CMD ["manage", "runserver", "0.0.0.0:8000"]
